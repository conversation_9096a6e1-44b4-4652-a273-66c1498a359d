package com.gapbot

import io.github.cdimascio.dotenv.Dotenv
import net.dv8tion.jda.api.JDABuilder
import net.dv8tion.jda.api.requests.GatewayIntent

fun main() {
    val dotenv = Dotenv.load()

    val jda = JDABuilder.createDefault(
        dotenv["BOT_TOKEN"],
        GatewayIntent.GUILD_MEMBERS,
        GatewayIntent.GUILD_MESSAGES,
        GatewayIntent.MESSAGE_CONTENT
    )
        .addEventListeners(App())
        .build()
        .awaitReady()

    println("GapBot is now online!")
}
