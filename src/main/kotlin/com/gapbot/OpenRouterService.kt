package com.gapbot

import io.github.cdimascio.dotenv.dotenv
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.bodyAsText
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

@Serializable
data class ChatMessage(
    val role: String,
    val content: String
)

@Serializable
data class ChatRequest(
    val model: String,
    val messages: List<ChatMessage>
)

@Serializable
data class ChatChoice(
    val message: ChatMessage,
    @SerialName("finish_reason")
    val finishReason: String? = null
)

@Serializable
data class ChatResponse(
    val choices: List<ChatChoice>? = null
)

class OpenRouterService(private val apiKey: String) {
    private val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
            })
        }
    }

    suspend fun generateResponse(userMessage: String, userName: String): String {
        return try {
            val dotenv = dotenv()
            val systemPrompt = dotenv["SYSTEM_PROMPT"] ?: "You are GapBot, a knowledgeable AI assistant specifically designed for a competitive robotics team's Discord server. Your primary mission is to foster collaboration, learning, and team spirit among students passionate about robotics and engineering."
            val request = ChatRequest(
                model = dotenv["MODEL"],
                messages = listOf(
                    ChatMessage(
                        role = "system",
                        content = systemPrompt
                    ),
                    ChatMessage(
                        role = "user",
                        content = "$userName says: $userMessage"
                    )
                )
            )

            val response: io.ktor.client.statement.HttpResponse = client.post("https://openrouter.ai/api/v1/chat/completions") {
                headers {
                    append(HttpHeaders.Authorization, "Bearer $apiKey")
                    append(HttpHeaders.ContentType, "application/json")
                }
                contentType(ContentType.Application.Json)
                setBody(request)
            }

            val responseBody = response.bodyAsText()
            println("OpenRouter API Response: $responseBody")

            if (response.status != HttpStatusCode.OK) {
                println("Error from OpenRouter API: ${response.status}")
                return "I'm sorry, I received an error from the AI service."
            }

            val chatResponse = response.body<ChatResponse>()
            chatResponse.choices?.firstOrNull()?.message?.content ?: "I'm sorry, I couldn't generate a response."
        } catch (e: Exception) {
            println("Error calling OpenRouter API: ${e.message}")
            "I'm experiencing some technical difficulties. Please try again later."
        }
    }

    fun close() {
        client.close()
    }
}
