package com.gapbot

import io.github.cdimascio.dotenv.dotenv
import net.dv8tion.jda.api.events.message.MessageReceivedEvent
import net.dv8tion.jda.api.hooks.ListenerAdapter
import kotlinx.coroutines.runBlocking

class App: ListenerAdapter() {
    private val dotenv = dotenv()
    private val openRouterService = OpenRouterService(dotenv["OPENROUTER_API_KEY"] ?: "")

    override fun onMessageReceived(event: MessageReceivedEvent) {
        // Don't respond to bots (including ourselves)
        if (event.author.isBot) return

        val chatChannel = dotenv["CHAT_CHANNEL"]

        // Respond if the message is in the designated chat channel OR if the bot is mentioned
        if (event.channel.id == chatChannel || event.message.mentions.users.contains(event.jda.selfUser)) {
            runBlocking {
                val response = aiRequestMethodHere(event.message.contentRaw, event.message.author.name)
                event.channel.sendMessage(response).queue()
            }
        }
    }

    private suspend fun aiRequestMethodHere(message: String, userName: String): String {
        return openRouterService.generateResponse(message, userName)
    }
}
